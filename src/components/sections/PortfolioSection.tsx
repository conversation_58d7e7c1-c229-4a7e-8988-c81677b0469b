// PIXELgarage Royal Premium Platform - Portfolio Section
// Showcase of premium projects with interactive gallery

'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useTranslations } from 'next-intl'
import { ExternalLink, Eye, Crown, Sparkles } from 'lucide-react'

const PortfolioSection = () => {
  // const t = useTranslations('portfolio')
  const [activeCategory, setActiveCategory] = useState('all')

  // Mock portfolio data - będziemy to zastąpić prawdziwymi projektami
  const projects = [
    {
      id: 1,
      title: 'Luxury Brand Identity',
      category: 'branding',
      image: '/api/placeholder/600/400',
      description: 'Kompleksowa identyfikacja wizualna dla ekskluzywnej marki jubilerskiej',
      tags: ['Branding', 'Logo Design', 'Visual Identity'],
      status: 'completed'
    },
    {
      id: 2,
      title: 'E-commerce Platform',
      category: 'web',
      image: '/api/placeholder/600/400',
      description: 'Nowoczesna platforma e-commerce z zaawansowanymi funkcjami',
      tags: ['Web Development', 'E-commerce', 'UX/UI'],
      status: 'completed'
    },
    {
      id: 3,
      title: 'Digital Transformation',
      category: 'automation',
      image: '/api/placeholder/600/400',
      description: 'Automatyzacja procesów biznesowych dla firmy technologicznej',
      tags: ['Automation', 'Integration', 'Strategy'],
      status: 'completed'
    },
    {
      id: 4,
      title: 'Content Creation Suite',
      category: 'content',
      image: '/api/placeholder/600/400',
      description: 'Seria materiałów video i graficznych dla influencera',
      tags: ['Video', 'Animation', 'Social Media'],
      status: 'completed'
    }
  ]

  const categories = [
    { id: 'all', label: 'Wszystkie' },
    { id: 'branding', label: 'Branding' },
    { id: 'web', label: 'Web Development' },
    { id: 'automation', label: 'Automatyzacja' },
    { id: 'content', label: 'Content Creation' }
  ]

  const filteredProjects = activeCategory === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeCategory)

  return (
    <section id="portfolio" className="section-padding relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          className="absolute top-1/3 left-1/4 w-80 h-80 bg-gold-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      </div>

      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <div className="inline-flex items-center gap-2 glass rounded-full px-6 py-3 mb-6">
            <Crown className="w-5 h-5 text-gold-400" />
            <span className="text-sm font-medium text-gold-400 font-display">
              NASZE DZIEŁA
            </span>
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            <span className="text-gradient-royal">NASZE DZIEŁA</span>
          </h2>

          <p className="text-xl text-platinum-300 max-w-3xl mx-auto">
            Projekty, które zmieniły reguły gry
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {categories.map((category) => (
            <motion.button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-gradient-royal text-white shadow-glow'
                  : 'glass text-platinum-300 hover:text-white hover:bg-white/10'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category.label}
            </motion.button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          layout
        >
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              className="group glass rounded-2xl overflow-hidden hover:shadow-royal transition-all duration-500"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -10 }}
              layout
            >
              {/* Project Image */}
              <div className="relative aspect-video overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-royal-500/20 to-gold-500/20 flex items-center justify-center">
                  <div className="text-center">
                    <Sparkles className="w-12 h-12 text-royal-400 mx-auto mb-2" />
                    <p className="text-sm text-platinum-400">
                      Projekt w przygotowaniu
                    </p>
                  </div>
                </div>
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-4">
                  <motion.button
                    className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Eye className="w-5 h-5 text-white" />
                  </motion.button>
                  <motion.button
                    className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <ExternalLink className="w-5 h-5 text-white" />
                  </motion.button>
                </div>
              </div>

              {/* Project Info */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-white mb-2 group-hover:text-gradient-royal transition-all duration-300">
                  {project.title}
                </h3>
                
                <p className="text-platinum-300 mb-4 text-sm leading-relaxed">
                  {project.description}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.tags.map((tag, tagIndex) => (
                    <span
                      key={tagIndex}
                      className="px-3 py-1 bg-white/10 rounded-full text-xs text-platinum-300"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <motion.button
                    className="flex-1 btn-secondary text-sm py-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Zobacz Projekt
                  </motion.button>
                  <motion.button
                    className="flex-1 btn-primary text-sm py-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Case Study
                  </motion.button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Coming Soon Notice */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="glass rounded-2xl p-8 max-w-2xl mx-auto">
            <Crown className="w-12 h-12 text-gold-400 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-white mb-4">
              Ekskluzywne Projekty w Przygotowaniu
            </h3>
            <p className="text-platinum-300 mb-6">
              Przygotowujemy spektakularne case studies naszych najnowszych projektów. 
              Każdy z nich to przełom w swojej kategorii.
            </p>
            <motion.button
              className="btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Bądź pierwszym, który je zobaczy
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default PortfolioSection
