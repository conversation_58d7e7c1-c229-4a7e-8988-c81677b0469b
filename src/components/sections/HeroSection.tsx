// PIXELgarage Royal Premium Platform - Hero Section
// Cinematic landing experience that converts visitors into clients

'use client'

import { useEffect, useRef, useState } from 'react'
import { motion, useAnimation } from 'framer-motion'
import { useTranslations } from 'next-intl'
import { ArrowRight, Play, Crown, Zap, Star, Sparkles } from 'lucide-react'
import CountUp from 'react-countup'
import { useInView } from 'react-intersection-observer'
import PremiumButton from '@/components/ui/PremiumButton'

const HeroSection = () => {
  // const t = useTranslations('hero')
  const controls = useAnimation()
  const [ref, inView] = useInView({ threshold: 0.3 })
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)

  useEffect(() => {
    if (inView) {
      controls.start('visible')
    }
  }, [controls, inView])

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 100, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  }

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [0, 5, -5, 0],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: 'easeInOut',
      },
    },
  }

  return (
    <section 
      ref={ref}
      className="relative min-h-screen flex items-center justify-center overflow-hidden section-padding"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        {/* Gradient Orbs */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-royal-500/20 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gold-500/20 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
          }}
        />

        {/* Floating Icons */}
        <motion.div
          className="absolute top-20 right-20"
          variants={floatingVariants}
          animate="animate"
        >
          <Crown className="w-16 h-16 text-gold-400 opacity-30" />
        </motion.div>
        <motion.div
          className="absolute bottom-32 left-16"
          variants={floatingVariants}
          animate="animate"
          transition={{ delay: 1 }}
        >
          <Sparkles className="w-12 h-12 text-royal-400 opacity-40" />
        </motion.div>
        <motion.div
          className="absolute top-1/2 right-16"
          variants={floatingVariants}
          animate="animate"
          transition={{ delay: 2 }}
        >
          <Star className="w-10 h-10 text-gold-500 opacity-35" />
        </motion.div>
      </div>

      <div className="container-custom relative z-10">
        <motion.div
          className="text-center max-w-6xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          {/* Ultra Premium Badge */}
          <motion.div
            variants={itemVariants}
            className="relative inline-flex items-center gap-3 glass rounded-full px-8 py-4 mb-8 border border-gold-400/30"
            whileHover={{ scale: 1.05, boxShadow: "0 0 50px rgba(251, 191, 36, 0.4)" }}
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            >
              <Crown className="w-6 h-6 text-gold-400" />
            </motion.div>
            <span className="text-sm font-bold text-gold-400 font-display tracking-wider">
              EXCLUSIVE DIGITAL EMPIRE
            </span>
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Zap className="w-5 h-5 text-royal-400" />
            </motion.div>
            <div className="absolute inset-0 bg-gradient-to-r from-gold-400/10 to-royal-400/10 rounded-full animate-pulse" />
          </motion.div>

          {/* Manipulative Headline */}
          <motion.h1
            variants={itemVariants}
            className="hero-title text-6xl md:text-8xl lg:text-9xl font-black mb-8 leading-none"
          >
            <motion.span
              className="block text-gradient-royal drop-shadow-2xl"
              whileHover={{ scale: 1.02 }}
            >
              TWÓJ BIZNES
            </motion.span>
            <motion.span
              className="block text-gradient-gold drop-shadow-2xl"
              whileHover={{ scale: 1.02 }}
            >
              POTRZEBUJE NAS
            </motion.span>
            <motion.span
              className="block text-2xl md:text-4xl text-platinum-300 font-normal mt-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
            >
              Nie chce. <span className="text-red-400 font-bold">POTRZEBUJE.</span>
            </motion.span>
          </motion.h1>

          {/* Manipulative Description */}
          <motion.div
            variants={itemVariants}
            className="max-w-5xl mx-auto mb-12"
          >
            <p className="text-2xl md:text-3xl text-platinum-200 mb-6 leading-relaxed font-medium">
              Podczas gdy Twoja konkurencja <span className="text-red-400 font-bold">umiera w cyfrowej ciemności</span>,
              my tworzymy <span className="text-gold-400 font-bold">cyfrowe imperia</span> dla wizjonerów.
            </p>
            <p className="text-lg md:text-xl text-platinum-400 leading-relaxed">
              Każdego dnia tracisz <span className="text-red-400 font-bold">miliony potencjalnych klientów</span>,
              którzy nigdy nie znajdą Twojego biznesu. My to zmieniamy.
              <span className="text-royal-400 font-bold"> Bezpowrotnie.</span>
            </p>
          </motion.div>

          {/* Ultra Premium CTA Buttons */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-8 justify-center items-center mb-20"
          >
            <PremiumButton
              variant="primary"
              size="xl"
              icon={<ArrowRight className="w-6 h-6" />}
              glowEffect={true}
              pulseEffect={true}
              className="hero-cta"
            >
              PRZEJMIJ KONTROLĘ NAD RYNKIEM
            </PremiumButton>

            <PremiumButton
              variant="secondary"
              size="xl"
              icon={<Play className="w-6 h-6" />}
              glowEffect={true}
              onClick={() => setIsVideoPlaying(true)}
              className="hero-cta"
            >
              ZOBACZ JAK NISZCZYMY KONKURENCJĘ
            </PremiumButton>
          </motion.div>

          {/* Dominance Indicators */}
          <motion.div
            variants={itemVariants}
            className="text-center"
          >
            <motion.p
              className="text-platinum-300 text-lg mb-4 font-bold"
              animate={{ opacity: [0.7, 1, 0.7] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              LIDERZY BRANŻY KTÓRZY JUŻ ZDOMINOWALI SWOJE RYNKI:
            </motion.p>

            <motion.div
              className="glass rounded-3xl p-8 mb-12 border border-gold-400/30"
              whileHover={{ scale: 1.02, boxShadow: "0 30px 60px rgba(157, 114, 255, 0.2)" }}
            >
              {/* Devastating Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-6xl mx-auto">
                {[
                  { number: 847, suffix: '%', label: 'WZROST SPRZEDAŻY KLIENTÓW', color: 'text-green-400' },
                  { number: 2.3, suffix: 'M€', label: 'WYGENEROWANE PRZYCHODY', color: 'text-gold-400' },
                  { number: 156, suffix: '', label: 'ZNISZCZONYCH KONKURENTÓW', color: 'text-red-400' },
                  { number: 99.7, suffix: '%', label: 'DOMINACJA RYNKOWA', color: 'text-royal-400' },
                ].map((stat, index) => (
                  <motion.div
                    key={index}
                    className="text-center relative"
                    variants={itemVariants}
                    transition={{ delay: index * 0.15 }}
                    whileHover={{ scale: 1.1, y: -10 }}
                  >
                    <motion.div
                      className={`text-4xl md:text-5xl font-black mb-3 ${stat.color} drop-shadow-lg`}
                      animate={{ scale: [1, 1.05, 1] }}
                      transition={{ duration: 2, repeat: Infinity, delay: index * 0.3 }}
                    >
                      {inView && (
                        <CountUp
                          end={stat.number}
                          duration={3}
                          delay={index * 0.3}
                          decimals={stat.suffix === 'M€' ? 1 : 0}
                        />
                      )}
                      {stat.suffix}
                    </motion.div>
                    <div className="text-xs text-platinum-300 font-bold tracking-wider uppercase">
                      {stat.label}
                    </div>
                    <motion.div
                      className="absolute -inset-2 bg-gradient-to-r from-transparent via-white/5 to-transparent rounded-xl"
                      animate={{ opacity: [0, 0.5, 0] }}
                      transition={{ duration: 3, repeat: Infinity, delay: index * 0.5 }}
                    />
                  </motion.div>
                ))}
              </div>

              <motion.div
                className="mt-8 text-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 2 }}
              >
                <p className="text-platinum-400 text-sm mb-2">
                  <span className="text-red-400 font-bold">UWAGA:</span> Te liczby to nie prognozy. To rzeczywistość naszych klientów.
                </p>
                <p className="text-xs text-platinum-500">
                  Twoja konkurencja już wie, że przegrała. Ty jeszcze nie.
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Scroll Indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          animate={{
            y: [0, 10, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <motion.div
              className="w-1 h-3 bg-white rounded-full mt-2"
              animate={{
                opacity: [0.3, 1, 0.3],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            />
          </div>
        </motion.div>
      </div>

      {/* Video Modal */}
      {isVideoPlaying && (
        <motion.div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onClick={() => setIsVideoPlaying(false)}
        >
          <motion.div
            className="relative max-w-4xl w-full aspect-video bg-black rounded-2xl overflow-hidden"
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="absolute inset-0 flex items-center justify-center text-white">
              <div className="text-center">
                <Play className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg">Demo Video Coming Soon</p>
                <p className="text-sm opacity-70 mt-2">
                  Przygotowujemy ekskluzywne materiały wideo
                </p>
              </div>
            </div>
            <button
              className="absolute top-4 right-4 w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
              onClick={() => setIsVideoPlaying(false)}
            >
              ×
            </button>
          </motion.div>
        </motion.div>
      )}
    </section>
  )
}

export default HeroSection
