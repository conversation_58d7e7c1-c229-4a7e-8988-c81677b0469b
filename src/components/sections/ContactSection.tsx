// PIXELgarage Royal Premium Platform - Contact Section
// Premium contact form with Tally integration and Calendly booking

'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useTranslations } from 'next-intl'
import {
  Mail,
  Phone,
  MapPin,
  Calendar,
  Crown,
  Sparkles,
  ArrowRight,
  ExternalLink
} from 'lucide-react'
import PremiumButton from '@/components/ui/PremiumButton'

const ContactSection = () => {
  // const t = useTranslations('contact')
  const [showCalendly, setShowCalendly] = useState(false)
  const [showTallyForm, setShowTallyForm] = useState(false)

  const contactInfo = [
    {
      icon: MapPin,
      label: 'Adres',
      value: 'Warszawa, Polska',
      description: 'Centrum biznesowe'
    },
    {
      icon: Mail,
      label: 'Email',
      value: '<EMAIL>',
      description: 'Odpowiadamy w 24h'
    },
    {
      icon: Phone,
      label: 'Telefon',
      value: '+48 123 456 789',
      description: 'Pon-Pt 9:00-18:00'
    }
  ]

  return (
    <section id="contact" className="section-padding relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10">
        <motion.div
          className="absolute top-1/4 left-1/3 w-96 h-96 bg-gold-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      </div>

      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <motion.div
            className="relative inline-flex items-center gap-3 glass rounded-full px-8 py-4 mb-8 border border-green-500/50"
            whileHover={{ scale: 1.05, boxShadow: "0 0 50px rgba(34, 197, 94, 0.4)" }}
          >
            <motion.div
              animate={{
                rotate: [0, 360],
                scale: [1, 1.2, 1]
              }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            >
              <Crown className="w-6 h-6 text-green-400" />
            </motion.div>
            <span className="text-sm font-bold text-green-400 font-display tracking-wider">
              OSTATNIA SZANSA NA DOMINACJĘ
            </span>
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              <Sparkles className="w-5 h-5 text-gold-400" />
            </motion.div>
            <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-gold-500/10 rounded-full animate-pulse" />
          </motion.div>

          <h2 className="text-5xl md:text-7xl lg:text-8xl font-black mb-8 leading-none">
            <motion.span
              className="block text-ultra-premium drop-shadow-2xl"
              whileHover={{ scale: 1.02 }}
            >
              PRZEJMIJ KONTROLĘ
            </motion.span>
            <motion.span
              className="block text-3xl md:text-4xl text-red-400 font-normal mt-4"
              animate={{ opacity: [0.7, 1, 0.7] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              Zanim zrobi to ktoś inny
            </motion.span>
          </h2>

          <p className="text-xl md:text-2xl text-platinum-200 max-w-4xl mx-auto font-bold leading-relaxed">
            Każda sekunda zwłoki to <span className="text-red-400">stracone miliony</span>.
            Twoja konkurencja już planuje przejęcie Twojego rynku.
            <br />
            <span className="text-green-400">My możemy to zatrzymać. Dziś.</span>
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Contact Information */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="glass rounded-3xl p-8">
              <h3 className="text-2xl font-bold text-white mb-6">
                Skontaktuj się z nami
              </h3>

              <div className="space-y-6">
                {contactInfo.map((info, index) => {
                  const Icon = info.icon
                  return (
                    <motion.div
                      key={index}
                      className="flex items-start gap-4"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.6, delay: index * 0.1 }}
                    >
                      <div className="w-12 h-12 bg-gradient-royal rounded-xl flex items-center justify-center flex-shrink-0">
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-white mb-1">
                          {info.label}
                        </h4>
                        <p className="text-royal-400 font-medium mb-1">
                          {info.value}
                        </p>
                        <p className="text-sm text-platinum-400">
                          {info.description}
                        </p>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="space-y-4">
              <motion.button
                onClick={() => setShowCalendly(true)}
                className="relative w-full glass rounded-2xl p-6 border border-royal-500/50 overflow-hidden group"
                whileHover={{
                  scale: 1.03,
                  y: -5,
                  boxShadow: "0 20px 40px rgba(157, 114, 255, 0.3)"
                }}
                whileTap={{ scale: 0.98 }}
              >
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-royal-500/20 to-purple-500/20"
                  initial={{ x: '-100%' }}
                  whileHover={{ x: '100%' }}
                  transition={{ duration: 0.6 }}
                />
                <div className="relative flex items-center gap-4">
                  <motion.div
                    className="w-14 h-14 bg-gradient-to-r from-royal-400 to-royal-600 rounded-xl flex items-center justify-center"
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  >
                    <Calendar className="w-7 h-7 text-white" />
                  </motion.div>
                  <div className="flex-1 text-left">
                    <h4 className="font-bold text-white mb-1 text-lg">
                      ZAREZERWUJ STRATEGICZNĄ SESJĘ
                    </h4>
                    <p className="text-sm text-royal-300 font-medium">
                      Bezpłatna analiza konkurencji + plan dominacji
                    </p>
                  </div>
                  <motion.div
                    animate={{ x: [0, 10, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRight className="w-6 h-6 text-royal-400" />
                  </motion.div>
                </div>
              </motion.button>

              <motion.button
                onClick={() => setShowTallyForm(true)}
                className="relative w-full glass rounded-2xl p-6 border border-gold-500/50 overflow-hidden group"
                whileHover={{
                  scale: 1.03,
                  y: -5,
                  boxShadow: "0 20px 40px rgba(251, 191, 36, 0.3)"
                }}
                whileTap={{ scale: 0.98 }}
              >
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-gold-500/20 to-orange-500/20"
                  initial={{ x: '-100%' }}
                  whileHover={{ x: '100%' }}
                  transition={{ duration: 0.6 }}
                />
                <div className="relative flex items-center gap-4">
                  <motion.div
                    className="w-14 h-14 bg-gradient-to-r from-gold-400 to-gold-600 rounded-xl flex items-center justify-center"
                    animate={{
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <Mail className="w-7 h-7 text-white" />
                  </motion.div>
                  <div className="flex-1 text-left">
                    <h4 className="font-bold text-white mb-1 text-lg">
                      WYŚLIJ ZAPYTANIE O DOMINACJĘ
                    </h4>
                    <p className="text-sm text-gold-300 font-medium">
                      Otrzymaj plan zniszczenia konkurencji w 24h
                    </p>
                  </div>
                  <motion.div
                    animate={{ x: [0, 10, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
                  >
                    <ArrowRight className="w-6 h-6 text-gold-400" />
                  </motion.div>
                </div>
              </motion.button>
            </div>
          </motion.div>

          {/* Contact Form Preview */}
          <motion.div
            className="glass rounded-3xl p-8"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="text-center mb-8">
              <motion.div
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.2, 1]
                }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              >
                <Crown className="w-20 h-20 text-gold-400 mx-auto mb-4 filter drop-shadow-lg" />
              </motion.div>
              <h3 className="text-3xl font-black text-ultra-premium mb-4">
                IMPERIUM CYFROWE
              </h3>
              <p className="text-platinum-200 font-bold text-lg">
                Nie tworzymy stron. <span className="text-red-400">Budujemy imperia</span>.
                <br />
                <span className="text-green-400">Twoja konkurencja będzie błagać o litość.</span>
              </p>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <motion.div
                  className="text-center p-6 bg-gradient-to-br from-royal-500/20 to-purple-500/20 rounded-xl border border-royal-400/30"
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <motion.div
                    className="text-3xl font-black text-gradient-royal mb-2"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    1h
                  </motion.div>
                  <div className="text-xs text-royal-300 font-bold uppercase tracking-wider">
                    CZAS ODPOWIEDZI
                  </div>
                </motion.div>
                <motion.div
                  className="text-center p-6 bg-gradient-to-br from-gold-500/20 to-orange-500/20 rounded-xl border border-gold-400/30"
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <motion.div
                    className="text-3xl font-black text-gradient-gold mb-2"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                  >
                    ∞%
                  </motion.div>
                  <div className="text-xs text-gold-300 font-bold uppercase tracking-wider">
                    DOMINACJA
                  </div>
                </motion.div>
              </div>

              <div className="text-center">
                <motion.p
                  className="text-sm text-platinum-300 mb-6 font-bold"
                  animate={{ opacity: [0.7, 1, 0.7] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <span className="text-red-400">UWAGA:</span> Miejsca ograniczone.
                  <br />
                  Przyjmujemy tylko <span className="text-gold-400">3 nowych klientów miesięcznie</span>.
                </motion.p>

                <PremiumButton
                  variant="danger"
                  size="lg"
                  icon={<ArrowRight className="w-5 h-5" />}
                  glowEffect={true}
                  pulseEffect={true}
                  onClick={() => setShowTallyForm(true)}
                  className="w-full"
                >
                  PRZEJMIJ KONTROLĘ TERAZ
                </PremiumButton>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Calendly Modal */}
      {showCalendly && (
        <motion.div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onClick={() => setShowCalendly(false)}
        >
          <motion.div
            className="relative max-w-4xl w-full h-[80vh] bg-white rounded-2xl overflow-hidden"
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-gray-600">
                <Calendar className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">Calendly Integration</p>
                <p className="text-sm opacity-70 mt-2">
                  Tutaj zostanie zintegrowany widget Calendly
                </p>
                <p className="text-xs opacity-50 mt-4">
                  API Token: {process.env.CALENDLY_API_TOKEN ? 'Skonfigurowany' : 'Brak'}
                </p>
              </div>
            </div>
            <button
              className="absolute top-4 right-4 w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
              onClick={() => setShowCalendly(false)}
            >
              ×
            </button>
          </motion.div>
        </motion.div>
      )}

      {/* Tally Form Modal */}
      {showTallyForm && (
        <motion.div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          onClick={() => setShowTallyForm(false)}
        >
          <motion.div
            className="relative max-w-4xl w-full h-[80vh] bg-white rounded-2xl overflow-hidden"
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-gray-600">
                <Mail className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">Tally Form Integration</p>
                <p className="text-sm opacity-70 mt-2">
                  Tutaj zostanie zintegrowany formularz Tally
                </p>
                <p className="text-xs opacity-50 mt-4">
                  API Key: {process.env.TALLY_API_KEY ? 'Skonfigurowany' : 'Brak'}
                </p>
              </div>
            </div>
            <button
              className="absolute top-4 right-4 w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
              onClick={() => setShowTallyForm(false)}
            >
              ×
            </button>
          </motion.div>
        </motion.div>
      )}
    </section>
  )
}

export default ContactSection
