// PIXELgarage Royal Premium Platform - Footer Component
// Premium footer with royal styling and comprehensive links

'use client'

import { motion } from 'framer-motion'
import { useTranslations } from 'next-intl'
import { 
  Crown, 
  Mail, 
  Phone, 
  MapPin,
  ArrowRight,
  ExternalLink,
  Heart,
  Sparkles
} from 'lucide-react'
import Link from 'next/link'

const Footer = () => {
  // const t = useTranslations('footer')
  const currentYear = new Date().getFullYear()

  const quickLinks = [
    { href: '#home', label: 'Strona Główna' },
    { href: '#services', label: 'Usługi' },
    { href: '#portfolio', label: 'Portfolio' },
    { href: '#pricing', label: 'Cennik' },
    { href: '#contact', label: 'Kontakt' }
  ]

  const services = [
    { href: '#branding', label: 'Brand Identity' },
    { href: '#web', label: 'Web Development' },
    { href: '#automation', label: 'Automatyzacja' },
    { href: '#content', label: 'Content Creation' },
    { href: '#transformation', label: 'Digital Transformation' }
  ]

  const legal = [
    { href: '/privacy', label: 'Polityka Prywatności' },
    { href: '/terms', label: 'Regulamin' },
    { href: '/cookies', label: 'Polityka Cookies' },
    { href: '/gdpr', label: 'RODO' }
  ]

  const scrollToSection = (href: string) => {
    const sectionId = href.replace('#', '')
    const element = document.getElementById(sectionId)
    
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  return (
    <footer className="relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border-t border-white/10">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-40 left-1/4 w-80 h-80 bg-royal-500/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="absolute -bottom-40 right-1/4 w-96 h-96 bg-gold-500/5 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 3,
          }}
        />
      </div>

      <div className="relative z-10">
        {/* Main Footer Content */}
        <div className="container-custom py-16">
          <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
            {/* Brand Section */}
            <motion.div
              className="lg:col-span-1"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="relative">
                  <Crown className="w-10 h-10 text-gold-400" />
                  <motion.div
                    className="absolute inset-0"
                    animate={{
                      rotate: [0, 360],
                    }}
                    transition={{
                      duration: 20,
                      repeat: Infinity,
                      ease: 'linear',
                    }}
                  >
                    <div className="w-10 h-10 border border-royal-400/30 rounded-full" />
                  </motion.div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gradient-royal font-display">
                    PIXEL<span className="text-gold-400">garage</span>
                  </h3>
                  <p className="text-xs text-platinum-400 -mt-1">
                    Royal Premium Agency
                  </p>
                </div>
              </div>

              <p className="text-platinum-300 mb-6 leading-relaxed">
                Ekskluzywna agencja cyfrowa tworząca przełomowe rozwiązania dla marek 
                pragnących dominować w cyfrowym świecie.
              </p>

              <div className="space-y-3">
                <div className="flex items-center gap-3 text-sm text-platinum-400">
                  <MapPin className="w-4 h-4 text-royal-400" />
                  <span>Warszawa, Polska</span>
                </div>
                <div className="flex items-center gap-3 text-sm text-platinum-400">
                  <Mail className="w-4 h-4 text-royal-400" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-3 text-sm text-platinum-400">
                  <Phone className="w-4 h-4 text-royal-400" />
                  <span>+48 123 456 789</span>
                </div>
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <h4 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
                <Sparkles className="w-5 h-5 text-royal-400" />
                Szybkie Linki
              </h4>
              <div className="space-y-3">
                {quickLinks.map((link, index) => (
                  <motion.button
                    key={link.href}
                    onClick={() => scrollToSection(link.href)}
                    className="block text-platinum-300 hover:text-white transition-colors duration-300 text-sm group"
                    whileHover={{ x: 5 }}
                  >
                    <span className="flex items-center gap-2">
                      {link.label}
                      <ArrowRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </span>
                  </motion.button>
                ))}
              </div>
            </motion.div>

            {/* Services */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h4 className="text-lg font-semibold text-white mb-6">
                Nasze Usługi
              </h4>
              <div className="space-y-3">
                {services.map((service, index) => (
                  <motion.button
                    key={service.href}
                    onClick={() => scrollToSection(service.href)}
                    className="block text-platinum-300 hover:text-white transition-colors duration-300 text-sm group"
                    whileHover={{ x: 5 }}
                  >
                    <span className="flex items-center gap-2">
                      {service.label}
                      <ArrowRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </span>
                  </motion.button>
                ))}
              </div>
            </motion.div>

            {/* Newsletter & Legal */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <h4 className="text-lg font-semibold text-white mb-6">
                Bądź na bieżąco
              </h4>
              
              <div className="glass rounded-2xl p-6 mb-6">
                <p className="text-sm text-platinum-300 mb-4">
                  Otrzymuj ekskluzywne insights o digital transformation
                </p>
                <div className="flex gap-2">
                  <input
                    type="email"
                    placeholder="Twój email"
                    className="flex-1 input text-sm py-2"
                  />
                  <motion.button
                    className="btn-primary px-4 py-2 text-sm"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <ArrowRight className="w-4 h-4" />
                  </motion.button>
                </div>
              </div>

              <div className="space-y-2">
                <h5 className="text-sm font-medium text-white mb-3">
                  Informacje Prawne
                </h5>
                {legal.map((item, index) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="block text-xs text-platinum-400 hover:text-platinum-300 transition-colors"
                  >
                    {item.label}
                  </Link>
                ))}
              </div>
            </motion.div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/10">
          <div className="container-custom py-6">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <motion.div
                className="flex items-center gap-2 text-sm text-platinum-400"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <span>© {currentYear} PIXELgarage. Wszystkie prawa zastrzeżone.</span>
              </motion.div>

              <motion.div
                className="flex items-center gap-2 text-sm text-platinum-400"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <span>Stworzone z</span>
                <Heart className="w-4 h-4 text-red-400" />
                <span>w Polsce</span>
              </motion.div>

              <motion.div
                className="flex items-center gap-4"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="flex items-center gap-2 text-xs text-platinum-500">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span>Wszystkie systemy działają</span>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
