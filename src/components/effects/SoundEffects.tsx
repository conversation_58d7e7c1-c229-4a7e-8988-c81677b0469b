// PIXELgarage Royal Premium Platform - Sound Effects
// Ultra-premium audio experience for royal platform

'use client'

import { useEffect } from 'react'

const SoundEffects = () => {
  useEffect(() => {
    // Create audio context for premium sound effects
    let audioContext: AudioContext | null = null
    
    const initAudio = () => {
      if (typeof window !== 'undefined' && !audioContext) {
        audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
      }
    }

    // Premium hover sound
    const playHoverSound = () => {
      if (!audioContext) return
      
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()
      
      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)
      
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.frequency.exponentialRampToValueAtTime(1200, audioContext.currentTime + 0.1)
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.1)
      
      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.1)
    }

    // Premium click sound
    const playClickSound = () => {
      if (!audioContext) return
      
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()
      
      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)
      
      oscillator.frequency.setValueAtTime(1000, audioContext.currentTime)
      oscillator.frequency.exponentialRampToValueAtTime(500, audioContext.currentTime + 0.05)
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.05)
      
      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.05)
    }

    // Success sound
    const playSuccessSound = () => {
      if (!audioContext) return
      
      const frequencies = [523.25, 659.25, 783.99] // C, E, G
      
      frequencies.forEach((freq, index) => {
        setTimeout(() => {
          const oscillator = audioContext!.createOscillator()
          const gainNode = audioContext!.createGain()
          
          oscillator.connect(gainNode)
          gainNode.connect(audioContext!.destination)
          
          oscillator.frequency.setValueAtTime(freq, audioContext!.currentTime)
          
          gainNode.gain.setValueAtTime(0, audioContext!.currentTime)
          gainNode.gain.linearRampToValueAtTime(0.15, audioContext!.currentTime + 0.01)
          gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext!.currentTime + 0.3)
          
          oscillator.start(audioContext!.currentTime)
          oscillator.stop(audioContext!.currentTime + 0.3)
        }, index * 100)
      })
    }

    // Add event listeners for premium interactions
    const addSoundEffects = () => {
      // Hover effects for buttons
      const buttons = document.querySelectorAll('button, .btn, .btn-primary, .btn-secondary')
      buttons.forEach(button => {
        button.addEventListener('mouseenter', () => {
          if (process.env.NEXT_PUBLIC_ENABLE_SOUND_EFFECTS === 'true') {
            playHoverSound()
          }
        })
        
        button.addEventListener('click', () => {
          if (process.env.NEXT_PUBLIC_ENABLE_SOUND_EFFECTS === 'true') {
            playClickSound()
          }
        })
      })

      // Success sound for form submissions
      const forms = document.querySelectorAll('form')
      forms.forEach(form => {
        form.addEventListener('submit', () => {
          if (process.env.NEXT_PUBLIC_ENABLE_SOUND_EFFECTS === 'true') {
            playSuccessSound()
          }
        })
      })
    }

    // Initialize on user interaction
    const handleFirstInteraction = () => {
      initAudio()
      addSoundEffects()
      document.removeEventListener('click', handleFirstInteraction)
      document.removeEventListener('touchstart', handleFirstInteraction)
    }

    document.addEventListener('click', handleFirstInteraction)
    document.addEventListener('touchstart', handleFirstInteraction)

    return () => {
      document.removeEventListener('click', handleFirstInteraction)
      document.removeEventListener('touchstart', handleFirstInteraction)
      
      if (audioContext) {
        audioContext.close()
      }
    }
  }, [])

  return null // This component doesn't render anything
}

export default SoundEffects
