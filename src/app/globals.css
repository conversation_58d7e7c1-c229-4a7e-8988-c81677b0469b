/* PIXELgarage Royal Premium Platform - Global Styles */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for Royal Premium Theme */
:root {
  /* Royal Color Palette */
  --royal-50: #f8f7ff;
  --royal-100: #f0edff;
  --royal-200: #e4ddff;
  --royal-300: #d1c2ff;
  --royal-400: #b89dff;
  --royal-500: #9d72ff;
  --royal-600: #8b4dff;
  --royal-700: #7c3aed;
  --royal-800: #6b21a8;
  --royal-900: #581c87;
  --royal-950: #3b0764;

  /* Gold Accent Colors */
  --gold-400: #fbbf24;
  --gold-500: #f59e0b;
  --gold-600: #d97706;

  /* Platinum Neutrals */
  --platinum-50: #f8fafc;
  --platinum-100: #f1f5f9;
  --platinum-200: #e2e8f0;
  --platinum-300: #cbd5e1;
  --platinum-400: #94a3b8;
  --platinum-500: #64748b;
  --platinum-600: #475569;
  --platinum-700: #334155;
  --platinum-800: #1e293b;
  --platinum-900: #0f172a;

  /* Spacing Scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Typography Scale */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-royal: 0 25px 50px -12px rgba(157, 114, 255, 0.25);
  --shadow-glow: 0 0 20px rgba(157, 114, 255, 0.4);
  --shadow-glow-lg: 0 0 40px rgba(157, 114, 255, 0.6);

  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

body {
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: var(--platinum-50);
  background: linear-gradient(135deg, var(--platinum-900) 0%, var(--platinum-800) 100%);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-playfair), serif;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.font-display {
  font-family: var(--font-orbitron), sans-serif;
  font-weight: 700;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--platinum-800);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--royal-500), var(--royal-700));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--royal-600), var(--royal-800));
}

/* Selection */
::selection {
  background: var(--royal-500);
  color: white;
}

::-moz-selection {
  background: var(--royal-500);
  color: white;
}

/* Focus Styles */
:focus {
  outline: 2px solid var(--royal-500);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

/* Ultra Premium Utility Classes */
@layer utilities {
  /* Advanced Text Gradients */
  .text-gradient-royal {
    background: linear-gradient(135deg, var(--royal-400), var(--royal-600), var(--royal-800));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 20px rgba(157, 114, 255, 0.5));
    animation: gradient-shift 3s ease-in-out infinite;
  }

  .text-gradient-gold {
    background: linear-gradient(135deg, var(--gold-400), var(--gold-600), #ff8c00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 0 20px rgba(251, 191, 36, 0.5));
    animation: gradient-shift 3s ease-in-out infinite reverse;
  }

  /* Ultra Premium Text Effects */
  .text-ultra-premium {
    background: linear-gradient(45deg, #9d72ff, #f59e0b, #9d72ff, #f59e0b);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: ultra-gradient 4s ease-in-out infinite;
    filter: drop-shadow(0 0 30px rgba(157, 114, 255, 0.8));
    text-shadow: 0 0 50px rgba(251, 191, 36, 0.6);
  }

  /* Background Gradients */
  .bg-gradient-royal {
    background: linear-gradient(135deg, var(--royal-500), var(--royal-700));
  }

  .bg-gradient-cosmic {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .bg-gradient-aurora {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }

  /* Glass Effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Glow Effects */
  .glow {
    box-shadow: var(--shadow-glow);
  }

  .glow-lg {
    box-shadow: var(--shadow-glow-lg);
  }

  .glow-hover:hover {
    box-shadow: var(--shadow-glow-lg);
    transition: box-shadow var(--transition-normal);
  }

  /* Animation Utilities */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-shimmer {
    position: relative;
    overflow: hidden;
  }

  .animate-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: shimmer 2s linear infinite;
  }

  /* Responsive Text */
  .text-responsive-xs { font-size: clamp(0.75rem, 2vw, 0.875rem); }
  .text-responsive-sm { font-size: clamp(0.875rem, 2.5vw, 1rem); }
  .text-responsive-base { font-size: clamp(1rem, 3vw, 1.125rem); }
  .text-responsive-lg { font-size: clamp(1.125rem, 3.5vw, 1.25rem); }
  .text-responsive-xl { font-size: clamp(1.25rem, 4vw, 1.5rem); }
  .text-responsive-2xl { font-size: clamp(1.5rem, 5vw, 1.875rem); }
  .text-responsive-3xl { font-size: clamp(1.875rem, 6vw, 2.25rem); }
  .text-responsive-4xl { font-size: clamp(2.25rem, 7vw, 3rem); }
  .text-responsive-5xl { font-size: clamp(3rem, 8vw, 3.75rem); }
  .text-responsive-6xl { font-size: clamp(3.75rem, 10vw, 4.5rem); }
}

/* Component Styles */
@layer components {
  /* Button Base */
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Ultra Premium Primary Button */
  .btn-primary {
    @apply btn text-white relative overflow-hidden;
    background: linear-gradient(135deg, #9d72ff 0%, #7c3aed 50%, #6b21a8 100%);
    border: 2px solid rgba(157, 114, 255, 0.5);
    box-shadow:
      0 20px 40px rgba(157, 114, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
      0 30px 60px rgba(157, 114, 255, 0.4),
      0 0 50px rgba(157, 114, 255, 0.6),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(157, 114, 255, 0.8);
  }

  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
  }

  .btn-primary:hover::before {
    left: 100%;
  }

  /* Ultra Premium Secondary Button */
  .btn-secondary {
    @apply btn text-white relative overflow-hidden;
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1), rgba(217, 119, 6, 0.1));
    border: 2px solid rgba(251, 191, 36, 0.5);
    backdrop-filter: blur(20px);
    box-shadow:
      0 15px 30px rgba(251, 191, 36, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-secondary:hover {
    transform: translateY(-2px) scale(1.02);
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.2), rgba(217, 119, 6, 0.2));
    border-color: rgba(251, 191, 36, 0.8);
    box-shadow:
      0 25px 50px rgba(251, 191, 36, 0.3),
      0 0 40px rgba(251, 191, 36, 0.4);
  }

  /* Card Base */
  .card {
    @apply glass rounded-2xl p-6 transition-all duration-300 hover:shadow-royal;
  }

  /* Input Base */
  .input {
    @apply w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-royal-500 focus:border-transparent transition-all duration-300;
  }

  /* Section Padding */
  .section-padding {
    @apply py-16 md:py-24 lg:py-32;
  }

  /* Container */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Ultra Premium Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-15px) rotate(2deg); }
  66% { transform: translateY(-25px) rotate(-2deg); }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(157, 114, 255, 0.4), 0 0 40px rgba(251, 191, 36, 0.2);
  }
  50% {
    box-shadow: 0 0 60px rgba(157, 114, 255, 0.8), 0 0 80px rgba(251, 191, 36, 0.4);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%) skewX(-15deg); }
  100% { transform: translateX(100%) skewX(-15deg); }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes ultra-gradient {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}

@keyframes royal-pulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 20px rgba(157, 114, 255, 0.5));
  }
  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 0 40px rgba(157, 114, 255, 0.8));
  }
}

@keyframes gold-shine {
  0% { transform: translateX(-100%) skewX(-15deg); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%) skewX(-15deg); opacity: 0; }
}

@keyframes matrix-rain {
  0% { transform: translateY(-100vh); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Print Styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}
