// PIXELgarage Royal Premium Platform - Root Layout
// Main application layout with royal premium styling and optimizations

import type { Metadata } from 'next'
import { Inter, Playfair_Display, JetBrains_Mono, Orbitron } from 'next/font/google'
// import { NextIntlClientProvider } from 'next-intl'
// import { getMessages } from 'next-intl/server'
import { Toaster } from 'react-hot-toast'
import './globals.css'

// Font configurations
const inter = Inter({
  subsets: ['latin', 'latin-ext'],
  variable: '--font-inter',
  display: 'swap',
})

const playfair = Playfair_Display({
  subsets: ['latin', 'latin-ext'],
  variable: '--font-playfair',
  display: 'swap',
})

const jetbrains = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains',
  display: 'swap',
})

const orbitron = Orbitron({
  subsets: ['latin'],
  variable: '--font-orbitron',
  display: 'swap',
})

// Metadata configuration
export const metadata: Metadata = {
  title: {
    default: 'PIXELgarage - Royal Premium Digital Agency',
    template: '%s | PIXELgarage',
  },
  description: 'Ekskluzywna agencja cyfrowa tworząca przełomowe rozwiązania dla marek pragnących dominować w cyfrowym świecie. Transformujemy biznesy przez strategię, design i technologię.',
  keywords: [
    'digital agency',
    'web development',
    'brand identity',
    'digital transformation',
    'automation',
    'premium design',
    'royal experience',
    'business growth',
  ],
  authors: [{ name: 'PIXELgarage Team' }],
  creator: 'PIXELgarage',
  publisher: 'PIXELgarage',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
    languages: {
      'pl': '/pl',
      'en': '/en',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'pl_PL',
    alternateLocale: 'en_US',
    url: '/',
    title: 'PIXELgarage - Royal Premium Digital Agency',
    description: 'Transformujemy biznesy w cyfrowe potęgi. Ekskluzywne rozwiązania dla marek pragnących dominować w cyfrowym świecie.',
    siteName: 'PIXELgarage',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'PIXELgarage - Royal Premium Digital Agency',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'PIXELgarage - Royal Premium Digital Agency',
    description: 'Transformujemy biznesy w cyfrowe potęgi. Ekskluzywne rozwiązania dla marek pragnących dominować w cyfrowym świecie.',
    images: ['/og-image.jpg'],
    creator: '@pixelgarage',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
}

// Viewport configuration
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#9d72ff' },
    { media: '(prefers-color-scheme: dark)', color: '#7c3aed' },
  ],
}

interface RootLayoutProps {
  children: React.ReactNode
}

export default function RootLayout({
  children,
}: RootLayoutProps) {
  // Simple messages object for now
  const messages = {
    hero: {
      title: "TWÓJ BIZNES POTRZEBUJE NAS",
      subtitle: "NIE CHCE. POTRZEBUJE.",
      description: "Podczas gdy Twoja konkurencja umiera w cyfrowej ciemności, my tworzymy cyfrowe imperia dla wizjonerów.",
      cta_primary: "PRZEJMIJ KONTROLĘ NAD RYNKIEM",
      cta_secondary: "ZOBACZ JAK NISZCZYMY KONKURENCJĘ",
      trusted_by: "LIDERZY BRANŻY KTÓRZY JUŻ ZDOMINOWALI SWOJE RYNKI:",
      stats: {
        projects: "WZROST SPRZEDAŻY KLIENTÓW",
        clients: "WYGENEROWANE PRZYCHODY",
        growth: "ZNISZCZONYCH KONKURENTÓW",
        experience: "DOMINACJA RYNKOWA"
      }
    },
    services: {
      title: "BROŃ MASOWEGO RAŻENIA",
      subtitle: "Dla biznesów gotowych na totalne zdominowanie swojej branży",
      categories: {
        brand_identity: {
          name: "IMPERIUM MARKI",
          description: "Tworzymy marki, które nie konkurują - one DOMINUJĄ. Twoja konkurencja będzie próbować Cię naśladować przez lata."
        },
        digital_transformation: {
          name: "CYFROWA DOMINACJA",
          description: "Nie modernizujemy - REWOLUCJONIZUJEMY. Twój biznes stanie się cyfrową potęgą, której konkurencja nie będzie w stanie dorównać."
        },
        web_development: {
          name: "ROZWÓJ STRON INTERNETOWYCH",
          description: "Nowoczesne, responsywne strony internetowe o wysokiej wydajności"
        },
        content_creation: {
          name: "TWORZENIE TREŚCI",
          description: "Profesjonalne treści wizualne i multimedialne dla Twojej marki"
        },
        automation: {
          name: "AUTOMATYZACJA",
          description: "Inteligentne rozwiązania automatyzujące procesy biznesowe"
        }
      }
    },
    portfolio: {
      title: "NASZE DZIEŁA",
      subtitle: "Projekty, które zmieniły reguły gry",
      view_project: "Zobacz Projekt",
      case_study: "Case Study"
    },
    pricing: {
      title: "WYBIERZ SWÓJ POZIOM WŁADZY",
      subtitle: "Inwestycja w dominację"
    },
    contact: {
      title: "PRZEJMIJ KONTROLĘ",
      subtitle: "Zanim zrobi to ktoś inny"
    },
    navigation: {
      home: "Strona Główna",
      services: "Usługi",
      portfolio: "Portfolio",
      pricing: "Cennik",
      contact: "Kontakt",
      book_consultation: "Umów Konsultację"
    }
  }

  return (
    <html
      lang="pl"
      className={`${inter.variable} ${playfair.variable} ${jetbrains.variable} ${orbitron.variable}`}
      suppressHydrationWarning
    >
      <head>
        {/* Preload critical fonts */}
        <link
          rel="preload"
          href="/fonts/inter-var.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        
        {/* Critical CSS for above-the-fold content */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Critical CSS for initial paint */
            body { 
              font-family: var(--font-inter), system-ui, sans-serif;
              background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
              color: #f8fafc;
              margin: 0;
              padding: 0;
              overflow-x: hidden;
            }
            
            /* Loading state */
            .loading-screen {
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: linear-gradient(135deg, #9d72ff 0%, #7c3aed 100%);
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 9999;
              transition: opacity 0.5s ease-out;
            }
            
            /* Smooth scrolling */
            html {
              scroll-behavior: smooth;
            }
            
            /* Custom scrollbar */
            ::-webkit-scrollbar {
              width: 8px;
            }
            
            ::-webkit-scrollbar-track {
              background: #1e293b;
            }
            
            ::-webkit-scrollbar-thumb {
              background: linear-gradient(135deg, #9d72ff, #7c3aed);
              border-radius: 4px;
            }
            
            ::-webkit-scrollbar-thumb:hover {
              background: linear-gradient(135deg, #8b4dff, #6b21a8);
            }
          `
        }} />
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://api.stripe.com" />
        <link rel="preconnect" href="https://api.calendly.com" />
        <link rel="preconnect" href="https://tally.so" />
        
        {/* DNS prefetch for performance */}
        <link rel="dns-prefetch" href="//images.unsplash.com" />
        <link rel="dns-prefetch" href="//cdn.pixelgarage.com" />
      </head>
      
      <body className="antialiased">
        {/* Loading screen */}
        <div id="loading-screen" className="loading-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white"></div>
        </div>
        
        {/* Main application */}
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
          {children}
        </div>

        {/* Toast notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'linear-gradient(135deg, #9d72ff 0%, #7c3aed 100%)',
              color: '#ffffff',
              border: '1px solid rgba(157, 114, 255, 0.3)',
              borderRadius: '12px',
              boxShadow: '0 25px 50px -12px rgba(157, 114, 255, 0.25)',
            },
            success: {
              iconTheme: {
                primary: '#10b981',
                secondary: '#ffffff',
              },
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#ffffff',
              },
            },
          }}
        />
        
        {/* Remove loading screen after hydration */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.addEventListener('load', function() {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                  loadingScreen.style.opacity = '0';
                  setTimeout(() => {
                    loadingScreen.style.display = 'none';
                  }, 500);
                }
              });
            `,
          }}
        />
        
        {/* Analytics script placeholder */}
        {process.env.NEXT_PUBLIC_GA_ID && (
          <>
            <script
              async
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
            />
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
                `,
              }}
            />
          </>
        )}
      </body>
    </html>
  )
}
