// PIXELgarage Royal Premium Platform - Calendly Integration
// Professional booking system integration

const CALENDLY_API_BASE = 'https://api.calendly.com'

if (!process.env.CALENDLY_API_TOKEN) {
  throw new Error('CALENDLY_API_TOKEN is not defined in environment variables')
}

const headers = {
  'Authorization': `Bearer ${process.env.CALENDLY_API_TOKEN}`,
  'Content-Type': 'application/json',
}

// Get user information
export async function getCalendlyUser() {
  try {
    const response = await fetch(`${CALENDLY_API_BASE}/users/me`, {
      headers,
    })

    if (!response.ok) {
      throw new Error(`Calendly API error: ${response.status}`)
    }

    const data = await response.json()
    return {
      success: true,
      user: data.resource,
    }
  } catch (error) {
    console.error('Error fetching Calendly user:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Get event types (available booking options)
export async function getEventTypes() {
  try {
    const userUuid = process.env.CALENDLY_USER_UUID
    if (!userUuid) {
      throw new Error('CALENDLY_USER_UUID is not defined')
    }

    const response = await fetch(
      `${CALENDLY_API_BASE}/event_types?user=${userUuid}&active=true`,
      { headers }
    )

    if (!response.ok) {
      throw new Error(`Calendly API error: ${response.status}`)
    }

    const data = await response.json()
    return {
      success: true,
      eventTypes: data.collection,
    }
  } catch (error) {
    console.error('Error fetching event types:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Get scheduled events
export async function getScheduledEvents(
  startTime?: string,
  endTime?: string,
  status: 'active' | 'canceled' = 'active'
) {
  try {
    const userUuid = process.env.CALENDLY_USER_UUID
    if (!userUuid) {
      throw new Error('CALENDLY_USER_UUID is not defined')
    }

    const params = new URLSearchParams({
      user: userUuid,
      status,
    })

    if (startTime) params.append('min_start_time', startTime)
    if (endTime) params.append('max_start_time', endTime)

    const response = await fetch(
      `${CALENDLY_API_BASE}/scheduled_events?${params}`,
      { headers }
    )

    if (!response.ok) {
      throw new Error(`Calendly API error: ${response.status}`)
    }

    const data = await response.json()
    return {
      success: true,
      events: data.collection,
      pagination: data.pagination,
    }
  } catch (error) {
    console.error('Error fetching scheduled events:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Get event details
export async function getEventDetails(eventUuid: string) {
  try {
    const response = await fetch(
      `${CALENDLY_API_BASE}/scheduled_events/${eventUuid}`,
      { headers }
    )

    if (!response.ok) {
      throw new Error(`Calendly API error: ${response.status}`)
    }

    const data = await response.json()
    return {
      success: true,
      event: data.resource,
    }
  } catch (error) {
    console.error('Error fetching event details:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Get invitees for an event
export async function getEventInvitees(eventUuid: string) {
  try {
    const response = await fetch(
      `${CALENDLY_API_BASE}/scheduled_events/${eventUuid}/invitees`,
      { headers }
    )

    if (!response.ok) {
      throw new Error(`Calendly API error: ${response.status}`)
    }

    const data = await response.json()
    return {
      success: true,
      invitees: data.collection,
    }
  } catch (error) {
    console.error('Error fetching event invitees:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Cancel an event
export async function cancelEvent(eventUuid: string, reason?: string) {
  try {
    const response = await fetch(
      `${CALENDLY_API_BASE}/scheduled_events/${eventUuid}/cancellation`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify({
          reason: reason || 'Canceled by organizer',
        }),
      }
    )

    if (!response.ok) {
      throw new Error(`Calendly API error: ${response.status}`)
    }

    const data = await response.json()
    return {
      success: true,
      cancellation: data.resource,
    }
  } catch (error) {
    console.error('Error canceling event:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Generate booking URL for embedding
export function generateBookingUrl(
  eventTypeSlug: string,
  prefill?: {
    name?: string
    email?: string
    customAnswers?: Record<string, string>
  }
) {
  const baseUrl = `https://calendly.com/pixelgarage/${eventTypeSlug}`
  
  if (!prefill) return baseUrl

  const params = new URLSearchParams()
  
  if (prefill.name) params.append('name', prefill.name)
  if (prefill.email) params.append('email', prefill.email)
  
  if (prefill.customAnswers) {
    Object.entries(prefill.customAnswers).forEach(([key, value]) => {
      params.append(`a${key}`, value)
    })
  }

  return `${baseUrl}?${params.toString()}`
}

// Webhook verification (for Calendly webhooks)
export function verifyCalendlyWebhook(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    const crypto = require('crypto')
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('base64')
    
    return signature === expectedSignature
  } catch (error) {
    console.error('Webhook verification failed:', error)
    return false
  }
}

export default {
  getUser: getCalendlyUser,
  getEventTypes,
  getScheduledEvents,
  getEventDetails,
  getEventInvitees,
  cancelEvent,
  generateBookingUrl,
  verifyWebhook: verifyCalendlyWebhook,
}
