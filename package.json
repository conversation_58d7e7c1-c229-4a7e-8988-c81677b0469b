{"name": "pixelgarage-royal-platform", "version": "1.0.0", "description": "Universe-grade royal premium digital agency platform - PIXELgarage", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio"}, "dependencies": {"next": "^15.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.5.0", "@types/node": "^22.0.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "framer-motion": "^11.11.0", "gsap": "^3.12.5", "@gsap/react": "^2.1.1", "three": "^0.169.0", "@react-three/fiber": "^8.17.10", "@react-three/drei": "^9.114.0", "lenis": "^1.1.13", "locomotive-scroll": "^5.0.0-beta.21", "lottie-react": "^2.4.0", "@rive-app/react-canvas": "^4.17.3", "prisma": "^5.22.0", "@prisma/client": "^5.22.0", "stripe": "^17.3.0", "@stripe/stripe-js": "^4.8.0", "zustand": "^5.0.0", "@tanstack/react-query": "^5.59.0", "next-intl": "^3.22.0", "clsx": "^2.1.1", "class-variance-authority": "^0.7.0", "tailwind-merge": "^2.5.3", "lucide-react": "^0.451.0", "react-intersection-observer": "^9.13.1", "react-use-gesture": "^9.1.3", "use-sound": "^4.0.3", "canvas-confetti": "^1.9.3", "react-hot-toast": "^2.4.1", "embla-carousel-react": "^8.3.0", "react-countup": "^6.5.3", "react-parallax-tilt": "^1.7.237"}, "devDependencies": {"eslint": "^9.0.0", "eslint-config-next": "^15.3.0", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/aspect-ratio": "^0.4.2", "@types/three": "^0.169.0", "@types/locomotive-scroll": "^4.1.3", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8"}, "engines": {"node": ">=18.17.0"}, "keywords": ["digital-agency", "royal-premium", "next.js", "react", "typescript", "3d-animations", "gsap", "three.js", "stripe", "calendly", "tally"], "author": "PIXELgarage Digital Agency", "license": "PRIVATE"}